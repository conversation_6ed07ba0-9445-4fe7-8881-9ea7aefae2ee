import { MainLayout } from "@/components/MainLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Free PDF & Image Tools Online - Compress, Convert, Merge PDFs",
  description:
    "Free online PDF and image tools. Compress PDFs, merge PDFs, convert images, resize photos, and more. No registration required. 100% secure and fast processing.",
  keywords: [
    "PDF tools",
    "image tools",
    "compress PDF",
    "merge PDF",
    "split PDF",
    "PDF converter",
    "image compressor",
    "image converter",
    "resize image",
    "free PDF tools",
    "online PDF editor",
    "PDF utilities",
    "image utilities",
  ],
  openGraph: {
    title: "FreeFox - Free PDF & Image Tools Online",
    description:
      "Free online PDF and image tools. Compress PDFs, merge PDFs, convert images, resize photos, and more. No registration required.",
    url: "https://freefox.com",
    type: "website",
  },
  alternates: {
    canonical: "https://freefox.com",
  },
};

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "FreeFox",
  description:
    "Free online PDF and image tools for compression, conversion, merging, and more",
  url: "https://freefox.com",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "PDF Compression",
    "PDF Merging",
    "PDF Splitting",
    "Image Compression",
    "Image Conversion",
    "Image Resizing",
  ],
};

export default function Home() {
  const pdfTools = [
    {
      title: "Compress PDF",
      description: "Reduce PDF file size while maintaining quality",
      href: "/compress-pdf",
      icon: "🗜️",
    },
    {
      title: "Merge PDF",
      description: "Combine multiple PDF files into one document",
      href: "/merge-pdf",
      icon: "📄",
    },
    {
      title: "Split PDF",
      description: "Extract pages or split PDF into multiple files",
      href: "/split-pdf",
      icon: "✂️",
    },
  ];

  const imageTools = [
    {
      title: "Compress Image",
      description: "Reduce image file size without losing quality",
      href: "/compress-image",
      icon: "🖼️",
    },
    {
      title: "Convert Image",
      description: "Convert between PNG, JPG, WebP formats",
      href: "/convert-image",
      icon: "🔄",
    },
    {
      title: "Resize Image",
      description: "Change image dimensions and resolution",
      href: "/resize-image",
      icon: "📐",
    },
  ];

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <section className="text-center py-12 space-y-6">
            <h1 className="text-5xl font-bold tracking-tight font-display">
              Free PDF & Image Tools Online
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Professional-grade PDF and image processing tools. Compress,
              convert, merge, and edit your files instantly. No registration
              required, 100% secure, and completely free.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="px-8">
                <Link href="/pdf-tools">Explore PDF Tools</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="px-8">
                <Link href="/image-tools">Explore Image Tools</Link>
              </Button>
            </div>
          </section>

          {/* PDF Tools Section */}
          <section className="py-12">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-display mb-4">
                PDF Tools
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Powerful PDF utilities to handle all your document processing
                needs
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {pdfTools.map((tool) => (
                <Card
                  key={tool.href}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="text-3xl mb-2">{tool.icon}</div>
                    <CardTitle className="font-display">{tool.title}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                    <Button asChild className="mt-4">
                      <Link href={tool.href}>Use Tool</Link>
                    </Button>
                  </CardHeader>
                </Card>
              ))}
            </div>
            <div className="text-center">
              <Button asChild variant="outline">
                <Link href="/pdf-tools">View All PDF Tools</Link>
              </Button>
            </div>
          </section>

          {/* Image Tools Section */}
          <section className="py-12">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-display mb-4">
                Image Tools
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Advanced image processing tools for all your photo editing needs
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {imageTools.map((tool) => (
                <Card
                  key={tool.href}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="text-3xl mb-2">{tool.icon}</div>
                    <CardTitle className="font-display">{tool.title}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                    <Button asChild className="mt-4">
                      <Link href={tool.href}>Use Tool</Link>
                    </Button>
                  </CardHeader>
                </Card>
              ))}
            </div>
            <div className="text-center">
              <Button asChild variant="outline">
                <Link href="/image-tools">View All Image Tools</Link>
              </Button>
            </div>
          </section>

          {/* Features Section */}
          <section className="py-12 bg-muted/20 rounded-lg">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-display mb-4">
                Why Choose FreeFox?
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl mb-4">🔒</div>
                <h3 className="text-xl font-semibold mb-2">100% Secure</h3>
                <p className="text-muted-foreground">
                  Your files are processed securely and deleted immediately
                  after processing
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">⚡</div>
                <h3 className="text-xl font-semibold mb-2">Lightning Fast</h3>
                <p className="text-muted-foreground">
                  Advanced algorithms ensure quick processing without
                  compromising quality
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">🆓</div>
                <h3 className="text-xl font-semibold mb-2">Completely Free</h3>
                <p className="text-muted-foreground">
                  No registration, no hidden fees, no watermarks - just free
                  tools
                </p>
              </div>
            </div>
          </section>
        </div>
      </MainLayout>
    </>
  );
}
