"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "sonner";

const imageResizeFAQs = [
  {
    question: "What image formats can I resize?",
    answer:
      "You can resize JPG, PNG, WebP, GIF, BMP, and TIFF images. All formats are supported with high-quality output.",
  },
  {
    question: "Will resizing affect image quality?",
    answer:
      "We use high-quality resampling algorithms to preserve image quality. Enlarging images may reduce sharpness, while reducing size typically maintains good quality.",
  },
  {
    question: "What does 'maintain aspect ratio' mean?",
    answer:
      "When enabled, the image proportions stay the same to prevent distortion. The image will fit within your specified dimensions but may be smaller than requested.",
  },
  {
    question: "Can I resize images to very large dimensions?",
    answer:
      "Yes, but enlarging images beyond their original size may result in pixelation or blurriness. For best results, avoid enlarging by more than 200%.",
  },
  {
    question: "Is there a limit on file size?",
    answer:
      "No, there are no file size limits. You can resize large high-resolution images without restrictions.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Image Resizer",
  description: "Free online image resizer to change image dimensions and size",
  url: "https://freefox.com/resize-image",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Resize images",
    "Maintain aspect ratio",
    "Custom dimensions",
    "High quality output",
  ],
};

export default function ResizeImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [width, setWidth] = useState(800);
  const [height, setHeight] = useState(600);
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);

  useEffect(() => {
    document.title = "Free Image Resizer Online - Resize Images | FreeFox";
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Resize images online for free. Change image dimensions while maintaining quality. Support for JPG, PNG, WebP and more. No registration required."
      );
    }
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/resize-image");
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
  };

  const handleResize = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to resize");
      return;
    }

    if (width <= 0 || height <= 0) {
      toast.error("Width and height must be positive values");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("width", width.toString());
      formData.append("height", height.toString());
      formData.append("maintain_aspect_ratio", maintainAspectRatio.toString());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("resize-image", formData);

      setProgress(90);

      // Use the utility function to handle the download
      await downloadFromResponse(response, `resized_${files[0].name}`);

      setProgress(100);
      setShowConfetti(true);
      toast.success("Image resized successfully");
    } catch (error) {
      console.error("Error resizing image:", error);
      toast.error("Failed to resize image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto space-y-8"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.div className="text-center space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold tracking-tight font-display">
            Image Resizer
          </h1>
          <p className="text-muted-foreground">
            Resize your images to specific dimensions
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{
              "image/jpeg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/webp": [".webp"],
              "image/gif": [".gif"],
              "image/bmp": [".bmp"],
              "image/tiff": [".tiff", ".tif"],
            }}
            maxFiles={1}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
            replaceExisting={true}
          />

          {files.length > 0 && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">Resize Options</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="width">Width (px)</Label>
                    <Input
                      id="width"
                      type="number"
                      min="1"
                      value={width}
                      onChange={(e) => setWidth(parseInt(e.target.value) || 0)}
                      disabled={isProcessing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height">Height (px)</Label>
                    <Input
                      id="height"
                      type="number"
                      min="1"
                      value={height}
                      onChange={(e) => setHeight(parseInt(e.target.value) || 0)}
                      disabled={isProcessing}
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="maintain-aspect-ratio"
                    checked={maintainAspectRatio}
                    onCheckedChange={(checked) =>
                      setMaintainAspectRatio(checked === true)
                    }
                    disabled={isProcessing}
                  />
                  <Label
                    htmlFor="maintain-aspect-ratio"
                    className="text-sm font-normal"
                  >
                    Maintain aspect ratio
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  {maintainAspectRatio
                    ? "The image will be resized to fit within the specified dimensions while maintaining its original aspect ratio."
                    : "The image will be stretched or compressed to exactly match the specified dimensions."}
                </p>
              </div>
            </div>
          )}
          {files.length > 0 && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleResize}
                  disabled={isProcessing}
                  className="px-8"
                >
                  {isProcessing ? "Resizing..." : "Resize Image"}
                </Button>
              </motion.div>
            </div>
          )}
        </motion.div>

        <motion.div
          className="bg-muted/30 p-6 rounded-lg space-y-4"
          variants={itemVariants}
        >
          <h2 className="text-xl font-bold font-display">
            About Image Resizing
          </h2>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>
              Our image resizer allows you to change the dimensions of your
              images. This is useful for:
            </p>
            <ul className="list-disc list-inside space-y-1">
              <li>Preparing images for websites or social media</li>
              <li>Reducing file size by decreasing dimensions</li>
              <li>Creating thumbnails or previews</li>
              <li>Standardizing image sizes for a collection</li>
            </ul>
            <p>
              When "Maintain aspect ratio" is enabled, the image will be resized
              proportionally to fit within the specified dimensions. This
              prevents distortion but may result in dimensions smaller than
              requested.
            </p>
            <p>
              For best quality, we use a high-quality resampling algorithm that
              preserves details and minimizes artifacts.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
