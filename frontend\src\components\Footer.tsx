import Link from "next/link";
import { memo, useCallback, useRef, useState } from "react";
import { FAQ } from "./FAQ";

const FooterLink = memo(
  ({ href, children }: { href: string; children: React.ReactNode }) => (
    <Link
      href={href}
      className="text-sm text-muted-foreground hover:text-primary transition-colors duration-150"
    >
      {children}
    </Link>
  )
);

FooterLink.displayName = "FooterLink";

export function Footer() {
  const [showFAQ, setShowFAQ] = useState(false);
  const faqRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleFAQToggle = useCallback(() => {
    setShowFAQ((prev) => {
      const newState = !prev;
      if (newState) {
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
          scrollTimeoutRef.current = null;
        }

        scrollTimeoutRef.current = setTimeout(() => {
          if (faqRef.current) {
            const rect = faqRef.current.getBoundingClientRect();
            const targetPosition = window.pageYOffset + rect.top - 100;

            window.scrollTo({
              top: targetPosition,
              behavior: "smooth",
            });
          }
        }, 100);
      }

      return newState;
    });
  }, []);

  const handleFAQCollapse = useCallback(() => {
    setShowFAQ(false);
  }, []);

  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t mt-auto bg-background">
      <div className="container mx-auto px-4 py-4">
        {showFAQ && (
          <div
            ref={faqRef}
            className="mb-8 animate-in slide-in-from-top-2 fade-in duration-200"
            id="faq-section"
          >
            <FAQ onCollapse={handleFAQCollapse} />
          </div>
        )}

        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-sm text-muted-foreground">
              © {currentYear} FreeFox. All rights reserved.
            </p>
          </div>

          <div className="flex flex-wrap justify-center md:justify-end gap-x-4 gap-y-2 items-center">
            <button
              onClick={handleFAQToggle}
              className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors duration-150 flex items-center gap-1"
              aria-expanded={showFAQ}
              aria-controls="faq-section"
              type="button"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`h-4 w-4 transition-transform duration-200 ${
                  showFAQ ? "rotate-180" : ""
                }`}
              >
                <path d="M12 19V5" />
                <path d="m5 12 7-7 7 7" />
              </svg>
              {showFAQ ? "Hide FAQ" : "FAQ"}
            </button>

            <span
              className="hidden md:inline text-muted-foreground/30"
              aria-hidden="true"
            >
              |
            </span>

            <FooterLink href="/pdf-tools">PDF Tools</FooterLink>
            <FooterLink href="/image-tools">Image Tools</FooterLink>

            <span
              className="hidden md:inline text-muted-foreground/30"
              aria-hidden="true"
            >
              |
            </span>
            <FooterLink href="/terms">Terms & Conditions</FooterLink>
            <FooterLink href="/privacy">Privacy Policy</FooterLink>
          </div>
        </div>
      </div>
    </footer>
  );
}
