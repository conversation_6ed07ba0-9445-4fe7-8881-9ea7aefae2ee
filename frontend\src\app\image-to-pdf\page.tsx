"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const imageToPdfFAQs = [
  {
    question: "What image formats can I convert to PDF?",
    answer:
      "You can convert JPG, PNG, WebP, GIF, BMP, and TIFF images to PDF. All common image formats are supported.",
  },
  {
    question: "Can I convert multiple images to one PDF?",
    answer:
      "Yes! You can upload up to 10 images and they will be combined into a single PDF document, with each image on a separate page.",
  },
  {
    question: "What page sizes are available?",
    answer:
      "We support A4 (210 × 297 mm) and Letter (8.5 × 11 in) page sizes. Images can be fitted to the page or placed at original size.",
  },
  {
    question: "Will the image quality be preserved?",
    answer:
      "Yes, the conversion process preserves the original quality of your images. High-quality images will result in high-quality PDFs.",
  },
  {
    question: "In what order are images arranged in the PDF?",
    answer:
      "Images are arranged in the order they were uploaded. Each image appears on a separate page in the final PDF document.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Image to PDF Converter",
  description:
    "Free online tool to convert images to PDF documents with custom page settings",
  url: "https://freefox.com/image-to-pdf",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Convert images to PDF",
    "Multiple images support",
    "Custom page sizes",
    "Preserve quality",
  ],
};

export default function ImageToPDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [pageSize, setPageSize] = useState("A4");
  const [fitToPage, setFitToPage] = useState(true);

  useEffect(() => {
    document.title =
      "Free Image to PDF Converter Online - Convert Images to PDF | FreeFox";
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Convert images to PDF online for free. Combine multiple images into one PDF document. Support for JPG, PNG, WebP and more. No registration required."
      );
    }
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/image-to-pdf");
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one image file to convert");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the files
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });
      formData.append("page_size", pageSize);
      formData.append("fit_to_page", fitToPage.toString());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 4;
        });
      }, 300);

      setProgress(30);

      // Use the API utility instead of direct fetch
      const response = await apiPost("image-to-pdf", formData);

      setProgress(90);

      // Determine filename based on number of files
      let outputFilename = "images.pdf";
      if (files.length === 1) {
        // Use the original filename if only one file
        const baseName =
          files[0].name.substring(0, files[0].name.lastIndexOf(".")) ||
          files[0].name;
        outputFilename = `${baseName}.pdf`;
      }

      // Use the utility function to handle the download
      await downloadFromResponse(response, outputFilename);

      setProgress(100);
      setShowConfetti(true);
      toast.success("Images converted to PDF successfully");
    } catch (error) {
      console.error("Error converting images to PDF:", error);
      toast.error("Failed to convert images to PDF. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <MainLayout>
      <SuccessConfetti
        show={showConfetti}
        duration={3000}
        onComplete={() => setShowConfetti(false)}
      />

      <motion.div
        className="max-w-3xl mx-auto space-y-8"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <motion.div className="text-center space-y-2" variants={itemVariants}>
          <h1 className="text-3xl font-bold tracking-tight font-display">
            Image to PDF
          </h1>
          <p className="text-muted-foreground">
            Convert images to PDF documents with custom settings
          </p>
        </motion.div>

        <motion.div className="space-y-6" variants={itemVariants}>
          <EnhancedFileUpload
            acceptedFileTypes={{
              "image/jpeg": [".jpg", ".jpeg"],
              "image/png": [".png"],
              "image/webp": [".webp"],
              "image/gif": [".gif"],
              "image/bmp": [".bmp"],
              "image/tiff": [".tiff", ".tif"],
            }}
            maxFiles={10}
            onFilesSelected={handleFilesSelected}
            isProcessing={isProcessing}
            processingProgress={progress}
          />

          {files.length > 0 && (
            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
              <h3 className="font-medium font-display">PDF Options</h3>
              <div className="space-y-4">
                <div className="flex flex-col space-y-1">
                  <Label className="text-sm font-medium">Page Size</Label>
                  <Select
                    value={pageSize}
                    onValueChange={setPageSize}
                    disabled={isProcessing}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select page size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A4">A4 (210 × 297 mm)</SelectItem>
                      <SelectItem value="Letter">
                        Letter (8.5 × 11 in)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="fit-to-page"
                    checked={fitToPage}
                    onCheckedChange={(checked) =>
                      setFitToPage(checked === true)
                    }
                    disabled={isProcessing}
                  />
                  <Label htmlFor="fit-to-page" className="text-sm font-normal">
                    Fit images to page
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  When enabled, images will be scaled to fit within the page
                  while maintaining their aspect ratio. When disabled, images
                  will be placed at their original size (may be cropped if
                  larger than the page).
                </p>
              </div>
            </div>
          )}
          {files.length > 0 && (
            <div className="flex justify-end">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleConvert}
                  disabled={isProcessing}
                  className="px-8"
                >
                  {isProcessing ? "Converting..." : "Convert to PDF"}
                </Button>
              </motion.div>
            </div>
          )}
        </motion.div>

        <motion.div
          className="bg-muted/30 p-6 rounded-lg space-y-4"
          variants={itemVariants}
        >
          <h2 className="text-xl font-bold font-display">
            About Image to PDF Conversion
          </h2>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>
              Our Image to PDF converter allows you to create PDF documents from
              your images. This is useful for:
            </p>
            <ul className="list-disc list-inside space-y-1">
              <li>Creating documents from scanned images</li>
              <li>Combining multiple images into a single PDF</li>
              <li>Preparing images for printing or sharing</li>
              <li>Creating professional documents from photos</li>
            </ul>
            <p>
              Each image will be placed on a separate page in the PDF. If you
              select multiple images, they will be arranged in the order they
              were uploaded.
            </p>
            <p className="text-xs mt-2">
              Note: For best results, use high-quality images. The conversion
              process preserves the quality of your original images.
            </p>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  );
}
