"use client";

import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq, pdfCompressionFAQs } from "@/components/SEOFaq";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { But<PERSON> } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "PDF Compressor",
  description:
    "Free online PDF compression tool to reduce file size while maintaining quality",
  url: "https://freefox.com/compress-pdf",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Reduce PDF file size",
    "Maintain document quality",
    "No registration required",
    "Secure processing",
  ],
};

// Helper function to format bytes
function formatBytes(bytes: number, decimals = 2) {
  if (!bytes || bytes === 0) return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}

interface CompressionStats {
  originalSize: number;
  compressedSize: number;
  ratio: string;
  originalFilename: string;
}

export default function CompressPDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [compressionStats, setCompressionStats] =
    useState<CompressionStats | null>(null);
  const [uploaderKey, setUploaderKey] = useState(0); // Used to reset EnhancedFileUpload

  // Set page metadata on client side
  useEffect(() => {
    document.title =
      "Free PDF Compressor Online - Reduce PDF File Size | FreeFox";

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Compress PDF files online for free. Reduce PDF file size up to 90% while maintaining quality. No registration required. Fast, secure, and easy to use."
      );
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/compress-pdf");
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
    setCompressionStats(null); // Clear previous stats if a new file is selected
  };

  const handleCompress = async () => {
    if (files.length === 0) {
      toast.error("Please select a PDF file to compress");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setCompressionStats(null);

    const currentFile = files[0];

    try {
      setProgress(5);
      const formData = new FormData();
      formData.append("file", currentFile);

      // Start incremental progress updates
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return Math.round(prev + Math.random() * 3 + 1); // Increment by 1-4% randomly
        });
      }, 250);

      const response = await apiPost("compress-pdf", formData); // Ensure this returns the actual Response object

      clearInterval(progressInterval);
      setProgress(100);
      await downloadFromResponse(response, `compressed_${files[0].name}`);

      const originalSizeHeader = response.headers.get("X-Original-Size");
      const compressedSizeHeader = response.headers.get("X-Compressed-Size");
      const ratioHeader = response.headers.get("X-Compression-Ratio");

      if (originalSizeHeader && compressedSizeHeader && ratioHeader) {
        setCompressionStats({
          originalSize: parseInt(originalSizeHeader, 10),
          compressedSize: parseInt(compressedSizeHeader, 10),
          ratio: ratioHeader,
          originalFilename: currentFile.name,
        });
        toast.success(
          `"${currentFile.name}" compressed! Reduction: ${ratioHeader}`
        );
      } else {
        console.warn(
          "One or more compression headers were missing or null. Stats will not be displayed."
        );
        toast.success(
          `"${currentFile.name}" compressed and downloaded! (Stats unavailable)`
        );
      }

      setShowConfetti(true);
      setFiles([]); // Clear the file from parent state. This hides the "Compress PDF" button.
      // EnhancedFileUpload will be replaced by stats view if compressionStats is set.
    } catch (error) {
      console.error("Error compressing PDF file:", error);
      toast.error(
        `Failed to compress "${currentFile.name}". Please try again.`
      );
      setProgress(0);
    } finally {
      if (progress === 100) {
        setTimeout(() => {
          setIsProcessing(false);
          setProgress(0);
        }, 300);
      } else {
        setIsProcessing(false);
        setProgress(0);
      }
    }
  };

  const handleCompressAnother = () => {
    setCompressionStats(null);
    setFiles([]);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
    setUploaderKey((prevKey) => prevKey + 1); // Force EnhancedFileUpload to re-mount and reset
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div className="mb-8 space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free PDF Compressor Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Reduce PDF file size up to 90% while maintaining quality. No
                registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Free Forever
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ No File Size Limit
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Secure Processing
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          {compressionStats ? (
            <motion.div
              className="mt-8 p-6 bg-muted/20 rounded-lg text-center"
              variants={itemVariants}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <h2 className="text-2xl font-semibold mb-2">
                Compression Complete!
              </h2>
              <p className="text-lg mb-4 text-muted-foreground">
                <span className="font-medium">
                  {compressionStats.originalFilename}
                </span>{" "}
                has been compressed and downloaded.
              </p>
              <div className="space-y-2 text-left inline-block mb-6">
                <p>
                  <strong>Original Size:</strong>{" "}
                  {formatBytes(compressionStats.originalSize)}
                </p>
                <p>
                  <strong>Compressed Size:</strong>{" "}
                  {formatBytes(compressionStats.compressedSize)}
                </p>
                <p>
                  <strong>Reduction:</strong> {compressionStats.ratio}
                </p>
              </div>
              <div>
                <Button onClick={handleCompressAnother} className="px-8">
                  Compress Another PDF
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.div className="space-y-6" variants={itemVariants}>
              {" "}
              <EnhancedFileUpload
                key={uploaderKey}
                acceptedFileTypes={{ "application/pdf": [".pdf"] }}
                maxFiles={1}
                onFilesSelected={handleFilesSelected}
                isProcessing={isProcessing}
                processingProgress={progress}
                replaceExisting={true}
              />
              {files.length > 0 && ( // Only show button if files are selected
                <div className="flex justify-end">
                  {" "}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {" "}
                    <Button
                      onClick={handleCompress}
                      disabled={isProcessing}
                      className="px-8"
                    >
                      {isProcessing
                        ? progress < 20
                          ? "Preparing..."
                          : progress < 90
                          ? "Compressing..."
                          : progress < 100
                          ? "Downloading..."
                          : "Complete!"
                        : "Compress PDF"}
                    </Button>
                  </motion.div>
                </div>
              )}
            </motion.div>
          )}

          <motion.div className="mt-12 space-y-8" variants={itemVariants}>
            {/* How to Compress PDF Section */}
            <div className="bg-muted/50 p-6 rounded-lg">
              <h2 className="text-2xl font-semibold mb-4">
                How to Compress PDF Files Online
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl">📁</span>
                  </div>
                  <h3 className="font-semibold mb-2">1. Upload PDF</h3>
                  <p className="text-sm text-muted-foreground">
                    Select your PDF file or drag and drop it into the upload
                    area
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl">⚡</span>
                  </div>
                  <h3 className="font-semibold mb-2">2. Compress</h3>
                  <p className="text-sm text-muted-foreground">
                    Our advanced algorithms automatically compress your PDF
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-xl">💾</span>
                  </div>
                  <h3 className="font-semibold mb-2">3. Download</h3>
                  <p className="text-sm text-muted-foreground">
                    Download your compressed PDF file instantly
                  </p>
                </div>
              </div>
            </div>

            {/* Benefits Section */}
            <div className="bg-muted/50 p-6 rounded-lg">
              <h2 className="text-2xl font-semibold mb-4">
                Why Compress PDF Files?
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2">📧</span> Email Attachments
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Most email providers have file size limits. Compressed PDFs
                    ensure your documents can be sent without issues.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2">☁️</span> Cloud Storage
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Save valuable cloud storage space by reducing PDF file sizes
                    while maintaining document quality.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2">🌐</span> Web Upload
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Many websites have upload size restrictions. Compressed PDFs
                    load faster and meet size requirements.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2">💰</span> Cost Savings
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Reduce bandwidth costs and storage expenses with smaller
                    file sizes.
                  </p>
                </div>
              </div>
            </div>

            {/* Security Section */}
            <div className="bg-muted/50 p-6 rounded-lg">
              <h2 className="text-2xl font-semibold mb-4">
                Secure PDF Compression
              </h2>
              <div className="space-y-4 text-sm text-muted-foreground">
                <p>
                  <strong>Privacy First:</strong> Your PDF files are processed
                  securely on our servers and are automatically deleted after
                  compression. We never store, share, or access your documents.
                </p>
                <p>
                  <strong>Advanced Compression:</strong> Our tool uses
                  state-of-the-art compression algorithms that optimize images,
                  remove unnecessary metadata, and apply lossless compression
                  techniques to reduce file size without compromising quality.
                </p>
                <p>
                  <strong>No Registration Required:</strong> Start compressing
                  PDFs immediately without creating an account or providing
                  personal information.
                </p>
              </div>
            </div>
          </motion.div>

          {/* FAQ Section */}
          <motion.div variants={itemVariants}>
            <SEOFaq
              title="PDF Compression FAQ"
              faqs={pdfCompressionFAQs}
              toolName="PDF Compressor"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
